{"infer_nnunet": {"dataset_id": "011", "epochs": "100", "configuration": "2d"}, "train_nnunet": {"dataset_id": "018", "epochs": "5", "configuration": "2d"}, "🔮 Inférence nnU-Net (infer_nnunet.py)": {"input_path": "C:/Users/<USER>/OneDrive - EvidentScientific/Documents/4Corrosion/NDEtoImagesUniversel/images/Cor_Tube_5mm_Hydro_rast_all_0001/endviews_uint8_backup"}, "🔄 Convertir inférence 2D vers 3D (convertir_inference_2d_to_3d.py)": {"input_path": "C:/Users/<USER>/OneDrive - EvidentScientific/Documents/4Corrosion/Dataset/imagesTestInference_RGB", "output_path": "C:/Users/<USER>/OneDrive - EvidentScientific/Documents/4Corrosion/Dataset/imagesTestInference_RGB_3d"}, "📦 Convertir dataset 2D vers 3D (convertir_dataset_2d_to_3d.py)": {"input_path": "C:/Users/<USER>/OneDrive - EvidentScientific/Documents/4Corrosion/Dataset/nnUnet/nnUNet_raw/Dataset020_testSentinel/imagesTr", "labels_path": "C:/Users/<USER>/OneDrive - EvidentScientific/Documents/4Corrosion/Dataset/nnUnet/nnUNet_raw/Dataset020_testSentinel/labelsTr", "output_path": "C:/Users/<USER>/OneDrive - EvidentScientific/Documents/4Corrosion/Dataset/nnUnet/nnUNet_raw"}, "convertir_dataset_2d_to_3d": {"dataset_name": "Dataset021_testSentinel_3d"}, "🔍 Vérifier la forme d'un fichier NIfTI (check_shape.py)": {"file_path": "C:/Users/<USER>/OneDrive - EvidentScientific/Documents/4Corrosion/Dataset/nnUnet/nnUNet_raw/Dataset019_Uint8Test_3d/imagesTr/001_0000.nii.gz"}, "👁️ Visualiser un volume NIfTI (view_nifti_volume.py)": {"file_path": "C:/Users/<USER>/OneDrive - EvidentScientific/Documents/4Corrosion/Dataset/nnUnet/nnUNet_raw/Dataset021_testDifférenceSentinel_3d/labelsTr/005.nii.gz"}, "util_directory_tree.py": {"input_path": "C:/Users/<USER>/Documents/4Corrosion/azure_data_asset/data/TPI_calibprod_light/training"}, "nifti_check_shape.py": {"file_path": "C:/Users/<USER>/Documents/4Corrosion/Dataset/nnUnet/nnUNet_raw/Dataset002_test/test3.nii.gz"}, "nifti_view_volume.py": {"file_path": "C:/Users/<USER>/Documents/4Corrosion/Dataset/nnUnet/nnUNet_raw/Dataset002_test/test3.nii.gz"}, "conversion_dataset_2d_to_3d.py": {"input_path": "C:/Users/<USER>/OneDrive - EvidentScientific/Documents/4Corrosion/Dataset/nnUnet/nnUNet_raw/DatasetXXX_testDataset/imagesTr", "labels_path": "C:/Users/<USER>/OneDrive - EvidentScientific/Documents/4Corrosion/Dataset/nnUnet/nnUNet_raw/DatasetXXX_testDataset/labelsTr", "output_path": "C:/Users/<USER>/OneDrive - EvidentScientific/Documents/4Corrosion/Dataset/nnUnet/nnUNet_raw"}, "conversion_dataset_2d_to_3d": {"dataset_name": "Dataset_3d"}, "H5py_NdeToImages.py": {"files_path": "C:/Users/<USER>/Documents/4Corrosion/Dataset/nde/Qualification dataset/MX2-757-SC-PS-00575-Z-12000 IA Training.nde", "output_path": "C:/Users/<USER>/Documents/4Corrosion/Dataset/images from nde"}, "H5py_enlever_dossier_inutile.py": {"root_dir": "C:/Users/<USER>/Documents/4Corrosion/azure_data_asset/output/npz/binaire"}, "H5py_inversion.py": {"inversion_dir": "C:/Users/<USER>/Documents/4Corrosion/visualize_tool/images/Hydroform2 5.15 2"}, "H5py_openFile.py": {"nde_file": "C:/Users/<USER>/Documents/4Corrosion/Dataset/nde/Qualification dataset/MX2-757-SC-PS-00575-Z-12000 IA Training.nde"}, "H5py_compareFile.py": {"file1_path": "C:/Users/<USER>/Documents/4Corrosion/visualize_tool/ndes/nde/From Benoit Cabirol/Cor_Tube_5mm_Hydro_rast_all_0001.nde", "file2_path": "C:/Users/<USER>/Documents/4Corrosion/visualize_tool/ndes/nde/From Florin/Pipe_8mm_Hydroform_Rast_TGY_all_2025_05_23 05h10m27s.nde"}, "H5py_process_nde.py": {"source_dir": "C:/Users/<USER>/Documents/4Corrosion/Dataset/nde", "dest_dir": "C:/Users/<USER>/Documents/4Corrosion/Dataset/images from nde"}, "overlay.py": {"image_dir": "C:/Users/<USER>/Documents/4Corrosion/Results/inference/TPI_prod_20250128_PS00391_Z_16000_gr1_v4_raw_data/dataset original/TPI_prod_20250128_PS00391_Z_16000_gr1_v4_raw_data", "mask_dir": "C:/Users/<USER>/Documents/4Corrosion/Results/inference/TPI_prod_20250128_PS00391_Z_16000_gr1_v4_raw_data/dataset original/TPI_prod_20250128_PS00391_Z_16000_gr1_v4_segmentation_mask", "output_dir": "C:/Users/<USER>/Documents/4Corrosion/Results/inference/TPI_prod_20250128_PS00391_Z_16000_gr1_v4_raw_data/dataset original"}, "npz_view_volume.py": {"file_path": "C:/Users/<USER>/Documents/4Corrosion/Dataset/nnUnet/nnUNet_raw/Dataset002_test/test3.npz"}, "npz_export_png.py": {"input_path": "C:/Users/<USER>/Documents/4Corrosion/azure_data_asset/data/TPI_calibprod_light/validation/TPI_prod_20250128_PS00400_Z_46000_gr1_v4_segmentation_mask.npz", "output_path": "C:/Users/<USER>/Documents/4Corrosion/azure_data_asset/data/TPI_calibprod_light/validation/400"}, "h5_view_volume.py": {"file_path": "C:/Users/<USER>/Documents/4Corrosion/Dataset/nnUnet/nnUNet_raw/Dataset002_test/test3.h5"}, "h5_export_png.py": {"output_path": "C:/Users/<USER>/Documents/4Corrosion/Dataset/inference-Dataset028_TPI_calibprod_light", "input_path": "C:/Users/<USER>/Documents/4Corrosion/azure_data_asset/data/TPI_calibprod_light/validation/TPI_MASBlock_c_2024_08_28_30_g1_v4/raw_data.h5"}, "npz_export_dossier_png.py": {"input_folder": "C:/Users/<USER>/Documents/4Corrosion/azure_data_asset/data/TPI_calibprod_light/validation", "output_folder": "C:/Users/<USER>/Documents/4Corrosion/azure_data_asset/output_bu/npz/validation"}, "npz_view_label.py": {"file_path": "C:/Users/<USER>/Documents/4Corrosion/Dataset/nnUnet/nnUNet_raw/Dataset002_test/test.npz"}, "npz_check_shape.py": {"file_path": "C:/Users/<USER>/Documents/4Corrosion/azure_data_asset/data/TPI_calibprod_light/training/TPI_prod_20250128_PS00389_Z_19000_gr1_v4/volume_data/segmentation_mask.npz"}, "h5_view_label.py": {"file_path": "C:/Users/<USER>/Documents/4Corrosion/azure_data_asset/output/TPI_MASBlock_c_2024_08_28_10_g1_v5_raw_data.h5"}, "h5_check_shape.py": {"file_path": "C:/Users/<USER>/Documents/4Corrosion/Dataset/nnUnet/nnUNet_raw/Dataset002_test/test3.h5"}, "pngs_to_volume.py": {"png_folder_path": "C:/Users/<USER>/Documents/4Corrosion/Dataset/nnUnet/nnUNet_raw/Dataset002_test/imagesTr", "volume_output_path": "C:/Users/<USER>/Documents/4Corrosion/Dataset/nnUnet/nnUNet_raw/Dataset002_test/test3"}, "h5_export_dossier_png.py": {"input_folder": "C:/Users/<USER>/Documents/4Corrosion/azure_data_asset/output", "output_folder": "C:/Users/<USER>/Documents/4Corrosion/azure_data_asset/output_bu/h5/validation"}, "png_merge_classes.py": {"input_path": "C:/Users/<USER>/Documents/4Corrosion/Dataset/nnUnet/nnUNet_raw/Dataset028_TPI_calibprod_light/labelsTr", "output_path": "C:/Users/<USER>/Documents/4Corrosion/Dataset/nnUnet/nnUNet_raw/Dataset028_TPI_calibprod_light/Nouveau dossier"}, "png_merge_classes": {"input_path": "C:/Users/<USER>/Documents/4Corrosion/Dataset/nnUnet/nnUNet_raw/Dataset028_TPI_calibprod_light/labelsTr", "output_path": "C:/Users/<USER>/Documents/4Corrosion/Dataset/nnUnet/nnUNet_raw/Dataset028_TPI_calibprod_light/Nouveau dossier"}, "merge_side_by_side.py": {"folder_a": "C:/Users/<USER>/Documents/4Corrosion/Results/inference/TPI_prod_20250128_PS00391_Z_16000_gr1_v4_raw_data/dataset original/TPI_prod_20250128_PS00391_Z_16000_gr1_v4_raw_data", "folder_b": "C:/Users/<USER>/Documents/4Corrosion/Results/inference/TPI_prod_20250128_PS00391_Z_16000_gr1_v4_raw_data/dataset original/TPI_prod_20250128_PS00391_Z_16000_gr1_v4_segmentation_mask", "output_folder": "C:/Users/<USER>/Documents/4Corrosion/Results/inference/TPI_prod_20250128_PS00391_Z_16000_gr1_v4_raw_data/dataset original/Nouveau dossier"}, "prefix_subfolder_to_png.py": {"root_folder": "C:/Users/<USER>/Documents/4Corrosion/azure_data_asset/output_bu/h5/validation"}, "rotate_images.py": {"folder_path": "C:/Users/<USER>/Documents/4Corrosion/Dataset/images from nde/MX2-757-SC-PS-00383-Z-49000/Gr01/base/no_label/endviews_uint8/complete"}, "rotate_images": {"rotation_angle": "270"}}